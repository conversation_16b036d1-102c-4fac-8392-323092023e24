<x-filament::page>
    <div class="space-y-6 grid grid-cols-4">
        <div class="col-span-1">
            {{ $this->form }}
        </div>
        <div class="col-span-3">
            <div wire:ignore id="map" style="width: 100%; height: 500px"></div>
        </div>

        @if (!empty($this->mapData))
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">
                    Resumo dos Clientes por Roteiro ({{ count($this->mapData) }} encontrados)
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach (collect($this->mapData)->groupBy('itinerary') as $itinerary => $customers)
                        @php
                            $itineraryColorHex = $this->getItineraryColorHex($customers->first()['roteiro']);
                        @endphp
                        <div class="border rounded-lg overflow-hidden shadow-sm"
                             style="border-top: 4px solid {{ $itineraryColorHex }};">
                            <div class="p-4">
                                <div class="flex items-center mb-3">
                                    <div class="w-4 h-4 rounded-full mr-3 flex items-center justify-center text-white text-xs font-bold"
                                         style="background-color: {{ $itineraryColorHex }};">
                                    </div>
                                    <span class="font-semibold text-gray-900 ml-3">{{ $itinerary }}</span>
                                    <span class="ml-auto text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                                        {{ count($customers) }} {{ count($customers) === 1 ? 'cliente' : 'clientes' }}
                                    </span>
                                </div>
                                <div class="space-y-2">
                                    @foreach ($customers as $customer)
                                        <div class="text-sm border-l-2 pl-3 py-1"
                                             style="border-color: {{ $itineraryColorHex }};">
                                            <div class="font-medium text-gray-900">{{ $customer['name'] }}</div>
                                            @if (!empty($customer['tax_id_number']))
                                                <div class="text-gray-600 text-xs">
                                                    {{ strlen($customer['tax_id_number']) > 11 ? 'CNPJ' : 'CPF' }}: {{ $customer['friendly_tax_id_number'] }}
                                                </div>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        @if (!empty($this->queryResult))
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">
                    Resumo dos produtos ({{ count($this->queryResult) }} encontrados)
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <table class="">
                        <tbody>
                            @foreach ($this->queryResult as $product)
                                <tr class="border">
                                    <td>{{ $product->Item }}</td>
                                    <td>{{ $product->QTD_Ajustada }}</td>
                                    <td>{{ $product->Roteiro }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        @endif
        <script>
            let locations = [];
            document.addEventListener('livewire:init', () => {
                Livewire.on('update-map-data', (event) => {
                    initMap(event);
                })
            })
            // 2) Array de dados (ID, nome, latitude, longitude, roteiro)
        </script>
        {{-- @endif --}}
    </div>

    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCGsfXqz7uzaZYqKumbZGxr9U517r0GAmI&callback=initMap" async defer></script>
    <script>
        // 3) Função de inicialização do mapa
        function initMap(locations = []) {
            // Centraliza no primeiro ponto (ou você pode calcular a média)
            const center = {
                lat: -23.5489,
                lng: -46.6388
            };
            const map = new google.maps.Map(document.getElementById("map"), {
                zoom: 11,
                center: center
            });

            // 4) Cria um InfoWindow único para reaproveitar
            const infoWindow = new google.maps.InfoWindow();

            if (locations.length === 0) {
                return;
            }

            // 5) Loop para criar marcadores
            locations[0].forEach(loc => {
                const marker = new google.maps.Marker({
                    position: {
                        lat: loc.lat,
                        lng: loc.lng
                    },
                    map: map,
                    title: loc.name,
                    // opcional: cor do pin de acordo com roteiro
                    icon: {
                        url: `http://maps.google.com/mapfiles/ms/icons/${loc.roteiro}-dot.png`
                    }
                });

                // evento de clique para abrir InfoWindow
                marker.addListener("click", () => {
                    infoWindow.setContent(
                        `<strong>${loc.name}</strong><br/>ID: ${loc.id}<br/>Roteiro: ${loc.itinerary}`
                    );
                    infoWindow.open(map, marker);
                });
            });
        }
    </script>
</x-filament::page>
