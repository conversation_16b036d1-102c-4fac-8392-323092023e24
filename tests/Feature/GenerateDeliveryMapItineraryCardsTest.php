<?php

namespace Tests\Feature;

use App\Filament\Pages\Delivery\GenerateDeliveryMap;
use App\Models\Customer;
use App\Models\User;
use Tests\TestCase;

class GenerateDeliveryMapItineraryCardsTest extends TestCase
{
    public function test_get_itinerary_color_hex_returns_correct_hex_colors()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $page = new GenerateDeliveryMap();
        
        // Test color mappings
        $this->assertEquals('#3B82F6', $page->getItineraryColorHex('blue'));
        $this->assertEquals('#EF4444', $page->getItineraryColorHex('red'));
        $this->assertEquals('#10B981', $page->getItineraryColorHex('green'));
        $this->assertEquals('#8B5CF6', $page->getItineraryColorHex('purple'));
        $this->assertEquals('#F59E0B', $page->getItineraryColorHex('yellow'));
        $this->assertEquals('#F97316', $page->getItineraryColorHex('orange'));
        $this->assertEquals('#EC4899', $page->getItineraryColorHex('pink'));
        $this->assertEquals('#9CA3AF', $page->getItineraryColorHex('silver'));
        $this->assertEquals('#D97706', $page->getItineraryColorHex('gold'));
        $this->assertEquals('#92400E', $page->getItineraryColorHex('brown'));
        $this->assertEquals('#6B7280', $page->getItineraryColorHex('unknown'));
    }

    public function test_map_data_includes_tax_id_information()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        // Create test customers with different tax ID types
        $customerCNPJ = Customer::factory()->create([
            'tax_id_number' => '12345678000195', // CNPJ
            'trading_name' => 'Test Company CNPJ',
            'latitude' => '-23.5489',
            'longitude' => '-46.6388'
        ]);

        $customerCPF = Customer::factory()->create([
            'tax_id_number' => '12345678901', // CPF
            'trading_name' => 'Test Company CPF',
            'latitude' => '-23.5489',
            'longitude' => '-46.6388'
        ]);

        $page = new GenerateDeliveryMap();
        
        // Simulate form data
        $customerIds = $customerCNPJ->id . ";01/01/2024;100;1\n" . $customerCPF->id . ";01/01/2024;100;2";
        
        $mockGet = function($field) use ($customerIds) {
            return match($field) {
                'customer_ids' => $customerIds,
                'delivered_at' => '2024-01-01',
                default => null
            };
        };

        $page->updateMapData(new class($mockGet) {
            public function __construct(private $mockGet) {}
            public function __invoke($field) { return ($this->mockGet)($field); }
        });

        // Verify that mapData contains tax ID information
        $this->assertNotEmpty($page->mapData);
        
        $cnpjCustomer = collect($page->mapData)->firstWhere('id', $customerCNPJ->id);
        $cpfCustomer = collect($page->mapData)->firstWhere('id', $customerCPF->id);
        
        $this->assertNotNull($cnpjCustomer);
        $this->assertNotNull($cpfCustomer);
        
        $this->assertEquals('12345678000195', $cnpjCustomer['tax_id_number']);
        $this->assertEquals('12345678901', $cpfCustomer['tax_id_number']);
        
        $this->assertArrayHasKey('friendly_tax_id_number', $cnpjCustomer);
        $this->assertArrayHasKey('friendly_tax_id_number', $cpfCustomer);
    }
}
