<?php

namespace App\Filament\Pages\Delivery;

use App\Models\Customer;
use Carbon\Carbon;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Pages\Page;
use Illuminate\Support\Facades\DB;

class GenerateDeliveryMap extends Page
{
    use InteractsWithForms;

    protected static ?string $title = 'Criar mapa de entrega';
    protected static ?string $slug = 'create-delivery-map';
    protected static string $view = 'filament.pages.delivery.generate-delivery-map';

    public array $deliveries = [];
    public array $mapData = [];
    public bool $disabled = false;
    public string $customer_ids = '';
    public string $delivered_at = '';
    public array $queryResult = [];

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    protected function getFormSchema(): array
    {
        return [
            Section::make('Buscar clientes')
                ->compact()
                ->schema([
                    Grid::make(1)->schema([
                        Textarea::make('customer_ids')
                            ->label('Clientes (um ID por linha)')
                            ->rows(10)
                            ->columnSpan(1)
                            ->lazy()
                            ->afterStateUpdated(function (\Filament\Forms\Get $get): void {
                                $this->updateMapData($get);
                            }),
                        ]),
                    Grid::make(1)->schema([
                        TextInput::make('delivered_at')
                            ->label('Data de entrega')
                            ->type('date')
                            ->columnSpan(1)
                            ->lazy()
                            ->afterStateUpdated(function (\Filament\Forms\Get $get): void {
                                $this->updateMapData($get);
                            }),
                        ]),
                ]),
        ];
    }

    public function updateMapData(\Filament\Forms\Get $get): void
    {
        if (is_null($get('delivered_at')) || is_null($get('customer_ids')) || $get('customer_ids') === '') {
            $this->mapData = [];
            return;
        }

        $mappedCustomers = collect(explode("\n", $get('customer_ids')))
            ->filter(function (string $item): bool {
                $explodedCustomer = explode(';', $item);
                return !is_null($explodedCustomer[0]) && $explodedCustomer[0] !== '';
            })
            ->mapWithKeys(function (string $item): array {
                $explodedCustomer = explode(';', $item);

                return [
                    $explodedCustomer[0] => [
                        'date' => Carbon::createFromFormat('d/m/Y', $explodedCustomer[1])->format('Y-m-d'),
                        'percentage' => $explodedCustomer[2],
                        'itinerary' => $explodedCustomer[3],
                    ],
                ];
            })
            ->toArray();

        $customerIds = array_keys($mappedCustomers);

        /** @var \Illuminate\Support\Collection $customers */
        $customers = Customer::query()
            ->whereIn('id', $customerIds)
            ->whereNotNull('latitude')
            ->whereNotNull('longitude')
            ->get();

        /** @var \Illuminate\Support\Collection $groupedMapData */
        $groupedMapData = array_values(
            $customers
                ->map(fn(Customer $customer): array => [
                    'id' => $customer->id,
                    'name' => $customer->trading_name,
                    'tax_id_number' => $customer->tax_id_number,
                    'friendly_tax_id_number' => $customer->friendly_tax_id_number,
                    'lat' => (float) $customer->latitude,
                    'lng' => (float) $customer->longitude,
                    'roteiro' => $mappedCustomers[$customer->id]['itinerary'],
                ])
                ->groupBy('roteiro')
                ->toArray()
        );

        foreach ($groupedMapData as $key => $mapDataGroup) {
            $currentMapDataGroup = collect($mapDataGroup)->map(fn(array $mapData): array => [
                'id' => $mapData['id'],
                'name' => $mapData['name'],
                'tax_id_number' => $mapData['tax_id_number'],
                'friendly_tax_id_number' => $mapData['friendly_tax_id_number'],
                'lat' => $mapData['lat'],
                'lng' => $mapData['lng'],
                'itinerary' => $mapData['roteiro'],
                'roteiro' => $this->getItineraryColor((int) $key),
            ])->toArray();

            $this->mapData = array_merge($this->mapData, $currentMapDataGroup);
        }

        $this->dispatch('update-map-data', $this->mapData);

        $this->queryResult = DB::select("
            WITH dados_brutos AS (
                SELECT
                '{$get('customer_ids')}'
                AS texto
            ),
            sequencia AS (
            SELECT ROW_NUMBER() OVER () AS seq
            FROM customers
            LIMIT 300
            ),
            linhas_separadas AS (
            SELECT
                TRIM(REPLACE(SUBSTRING_INDEX(SUBSTRING_INDEX(db.texto, '\n', s.seq), '\n', -1), '\r', '')) AS linha
            FROM dados_brutos db
            JOIN sequencia s
                ON s.seq <= LENGTH(db.texto) - LENGTH(REPLACE(db.texto, '\n', ''))
            WHERE TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(db.texto, '\n', s.seq), '\n', -1)) <> ''
            ),
            dados_formatados AS (
            SELECT
                CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(linha, ';', 1), ';', -1) AS UNSIGNED) AS customer_id,
                STR_TO_DATE(SUBSTRING_INDEX(SUBSTRING_INDEX(linha, ';', 2), ';', -1), '%d/%m/%Y') AS collected_at,
                COALESCE(NULLIF(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(linha, ';', 3), ';', -1)), ''), '100') AS percentual,
                TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(linha, ';', 4), ';', -1)) AS roteiro
            FROM linhas_separadas
            ),
            itens_agrupados AS (
            SELECT
                p.name AS Item,
                SUM(ci.quantity * (df.percentual / 100)) AS QTD_Ajustada,
                df.roteiro AS Roteiro
            FROM dados_formatados df
            JOIN collections c ON c.customer_id = df.customer_id AND c.collected_at = df.collected_at
            JOIN collection_items ci ON ci.collection_id = c.id
            JOIN products p ON p.id = ci.product_id
            GROUP BY p.id, df.roteiro
            ),
            totais_por_roteiro AS (
            SELECT
                'TOTAL' AS Item,
                SUM(QTD_Ajustada) AS QTD_Ajustada,
                Roteiro
            FROM itens_agrupados
            GROUP BY Roteiro
            ),
            total_geral AS (
            SELECT
                'TOTAL' AS Item,
                SUM(QTD_Ajustada) AS QTD_Ajustada,
                'GERAL' AS Roteiro
            FROM itens_agrupados
            ),
            total_por_item AS (
            SELECT
                Item,
                SUM(QTD_Ajustada) AS QTD_Ajustada,
                'GERAL' AS Roteiro
            FROM itens_agrupados
            GROUP BY Item
            )
            SELECT * FROM total_geral
            UNION ALL
            SELECT * FROM totais_por_roteiro
            UNION ALL
            SELECT * FROM total_por_item
            UNION ALL
            SELECT * FROM itens_agrupados
            ORDER BY
            CASE
                WHEN Roteiro = 'GERAL' AND Item = 'TOTAL' THEN 1 -- total geral no topo
                WHEN Item = 'TOTAL' THEN 2                       -- totais por roteiro
                WHEN Roteiro = 'GERAL' THEN 3                    -- totais por item (geral)
                ELSE 4                                           -- detalhado
            END,
            Roteiro,
            QTD_Ajustada DESC
        ");
    }

    public function getItineraryColor(int $itinerary): string
    {
        switch ($itinerary) {
            case 0:
                return "blue";
            case 1:
                return "red";
            case 2:
                return "green";
            case 3:
                return "purple";
            case 4:
                return "yellow";
            case 5:
                return "orange";
            case 6:
                return "pink";
            case 7:
                return "silver";
            case 8:
                return "gold";
            case 9:
                return "brown";
            default:
                return "grey";
        }
    }

    public function getItineraryColorHex(string $itinerary): string
    {
        switch ($itinerary) {
            case 'blue':
                return '#3B82F6';
            case 'red':
                return '#EF4444';
            case 'green':
                return '#10B981';
            case 'purple':
                return '#8B5CF6';
            case 'yellow':
                return '#F59E0B';
            case 'orange':
                return '#F97316';
            case 'pink':
                return '#EC4899';
            case 'silver':
                return '#9CA3AF';
            case 'gold':
                return '#D97706';
            case 'brown':
                return '#92400E';
            default:
                return '#6B7280';
        }
    }
}
