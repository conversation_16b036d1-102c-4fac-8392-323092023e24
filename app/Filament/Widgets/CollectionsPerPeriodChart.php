<?php

namespace App\Filament\Widgets;

use App\Enums\RoleEnum;
use App\Models\Collection;
use Carbon\Carbon;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class CollectionsPerPeriodChart extends ApexChartWidget
{
    use InteractsWithPageFilters;

    protected static ?int $sort = 4;
    protected static ?string $chartId = 'collectionsPerPeriodChart';
    protected static ?string $heading = 'Coletas por período (em quantidade)';
    protected int | string | array $columnSpan = [
        'sm' => 12,
        'md' => 12,
        'lg' => 6,
        'xl' => 6,
    ];

    public static function canView(): bool
    {
        return auth()->user()->hasRole(RoleEnum::Administrator->value);
    }

    protected function getOptions(): array
    {
        $start = Carbon::parse($this->filters['start_date'])->setHour(3);
        $end = Carbon::parse($this->filters['end_date'])->addDay()->setHour(2)->setMinute(59)->setSecond(59);

        $data = Collection::query()
            ->select([
                'collected_at',
                DB::raw('count(1) as count')
            ])
            ->where('collected_at', '>=', $start->format('Y-m-d'))
            ->where('collected_at', '<=', $end->format('Y-m-d'))
            ->groupBy('collected_at')
            ->get()
            ->mapWithKeys(fn (Collection $collection): array => [
                format_date($collection->collected_at) => $collection->count
            ])
            ->toArray();

        return [
            'chart' => [
                'type' => 'line',
                'height' => 300,
            ],
            'series' => [
                [
                    'name' => 'Coletas por período',
                    'data' => array_values($data),
                ],
            ],
            'xaxis' => [
                'categories' => array_keys($data),
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
            ],
            'yaxis' => [
                'min' => 0,
                'forceNiceScale' => true,
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
            ],
            'stroke' => [
                'curve' => 'smooth',
            ],
        ];
    }
}
