<?php

namespace App\Filament\Widgets;

use App\Enums\RoleEnum;
use App\Models\Delivery;
use Carbon\Carbon;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class DeliveriesPerPeriodChart extends ApexChartWidget
{
    use InteractsWithPageFilters;

    protected static ?int $sort = 3;
    protected static ?string $chartId = 'deliveriesPerPeriodChart';
    protected static ?string $heading = 'Entregas por período (em quantidade)';
    protected int | string | array $columnSpan = [
        'sm' => 12,
        'md' => 12,
        'lg' => 6,
        'xl' => 6,
    ];

    public static function canView(): bool
    {
        return auth()->user()->hasRole(RoleEnum::Administrator->value);
    }

    protected function getOptions(): array
    {
        $start = Carbon::parse($this->filters['start_date'])->setHour(3);
        $end = Carbon::parse($this->filters['end_date'])->addDay()->setHour(2)->setMinute(59)->setSecond(59);

        $data = Delivery::query()
            ->select([
                'delivered_at',
                DB::raw('count(1) as count')
            ])
            ->where('delivered_at', '>=', $start->format('Y-m-d'))
            ->where('delivered_at', '<=', $end->format('Y-m-d'))
            ->groupBy('delivered_at')
            ->get()
            ->mapWithKeys(fn (Delivery $delivery): array => [
                format_date($delivery->delivered_at) => $delivery->count
            ])
            ->toArray();

        return [
            'chart' => [
                'type' => 'line',
                'height' => 300,
            ],
            'series' => [
                [
                    'name' => 'Entregas por período',
                    'data' => array_values($data),
                ],
            ],
            'xaxis' => [
                'categories' => array_keys($data),
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
            ],
            'yaxis' => [
                'min' => 0,
                'forceNiceScale' => true,
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
            ],
            'stroke' => [
                'curve' => 'smooth',
            ],
        ];
    }
}
